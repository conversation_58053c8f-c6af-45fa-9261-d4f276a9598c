# Monitoring Application Configuration Template
# Copy this file to .env and update the values according to your environment

# =============================================================================
# WLC (Wireless LAN Controller) Configuration
# =============================================================================

# IP address of the WLC to monitor
WLC_HOST=**************

# SNMP community string for WLC access
WLC_COMMUNITY=domotz

# Database timeout in seconds for WLC operations
WLC_DB_TIMEOUT=30.0

# =============================================================================
# Switch Monitoring Configuration
# =============================================================================

# SNMP community string for switch access
SWITCH_COMMUNITY=public

# Comma-separated list of switch IP addresses to monitor
# Example: **************,**************,**************
SWITCH_IPS=**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************,**************

# =============================================================================
# Camera Scanner Configuration
# =============================================================================

# Comma-separated list of network ranges to scan for cameras
# Example: **********/24,**********/24
CAMERA_SCAN_RANGES=**********/24,**********/24

# Comma-separated list of IP addresses to exclude from camera scanning
# These might be network infrastructure devices that respond on port 80 but aren't cameras
CAMERA_EXCLUDED_IPS=************,************,************,************,************,************,************

# Number of concurrent threads for camera scanning (adjust based on system capabilities)
CAMERA_THREAD_COUNT=50

# =============================================================================
# Flask Web Application Configuration
# =============================================================================

# Host IP address for the Flask web server
# Leave empty to use automatic detection (127.0.0.1 for Windows, *********** for Linux)
# FLASK_HOST=127.0.0.1

# Port for the Flask web server
FLASK_PORT=5050

# =============================================================================
# Database Configuration (Optional)
# =============================================================================

# Override default database paths if needed
# By default, databases are stored in the application directory on Windows
# and in /opt/wlc-monitor/ on Linux

# ACCESS_POINTS_DB_PATH=/path/to/access_points.db
# SWITCH_MONITORING_DB_PATH=/path/to/switch_monitoring.db
# NETWORK_DEVICES_DB_PATH=/path/to/network_devices.db

# =============================================================================
# Notes
# =============================================================================

# 1. Remove the '#' at the beginning of lines to uncomment them
# 2. Replace example values with your actual configuration
# 3. Keep sensitive information (like SNMP community strings) secure
# 4. IP addresses must be valid IPv4 addresses
# 5. Network ranges must be in CIDR notation (e.g., ***********/24)
# 6. Comma-separated lists should not have spaces around commas unless the spaces are part of the value
