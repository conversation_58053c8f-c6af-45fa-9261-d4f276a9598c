# Network Monitoring Application

A comprehensive network monitoring solution that tracks wireless access points, network switches, and IP cameras.

## Features

- **Access Point Monitoring**: Monitor Cisco WLC (Wireless LAN Controller) and connected access points
- **Switch Monitoring**: Monitor network switches via SNMP
- **Camera Detection**: Scan network ranges to detect IP cameras
- **Web Dashboard**: Real-time web interface showing device status and events
- **Event Logging**: Track device up/down events and status changes

## Configuration

The application uses environment variables for configuration, loaded from a `.env` file. This allows for easy deployment across different environments without code changes.

### Setup Configuration

1. **Copy the example configuration file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your specific network configuration:
   ```bash
   nano .env
   ```

### Configuration Variables

#### WLC (Wireless LAN Controller) Settings
- `WLC_HOST`: IP address of the WLC to monitor (required)
- `WLC_COMMUNITY`: SNMP community string for WLC access (required)
- `WLC_DB_TIMEOUT`: Database timeout in seconds (default: 30.0)

#### Switch Monitoring Settings
- `SWITCH_COMMUNITY`: SNMP community string for switch access (default: public)
- `SWITCH_IPS`: Comma-separated list of switch IP addresses to monitor (required)
  - For better readability, IPs are listed with comments below the setting in the .env file

#### Camera Scanner Settings
- `CAMERA_SCAN_RANGES`: Comma-separated list of network ranges to scan (default: **********/24,**********/24)
- `CAMERA_EXCLUDED_IPS`: Comma-separated list of IPs to exclude from scanning
- `CAMERA_THREAD_COUNT`: Number of concurrent scanning threads (default: 50)

#### Flask Web Application Settings
- `FLASK_HOST`: Host IP for the web server (auto-detected if not set)
- `FLASK_PORT`: Port for the web server (default: 5050)

#### Database Settings (Optional)
- `ACCESS_POINTS_DB_PATH`: Custom path for access points database
- `SWITCH_MONITORING_DB_PATH`: Custom path for switch monitoring database
- `NETWORK_DEVICES_DB_PATH`: Custom path for network devices database

### Example Configuration

```env
# WLC Configuration
WLC_HOST=**************
WLC_COMMUNITY=domotz
WLC_DB_TIMEOUT=30.0

# Switch Configuration
SWITCH_COMMUNITY=public
SWITCH_IPS=**************,**************,**************

# Camera Scanner Configuration
CAMERA_SCAN_RANGES=**********/24,**********/24
CAMERA_EXCLUDED_IPS=************,************
CAMERA_THREAD_COUNT=50

# Flask Configuration
FLASK_PORT=5050
```

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure the application:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run the application:**
   ```bash
   python app.py
   ```

## Usage

1. **Start the application:**
   ```bash
   python app.py
   ```

2. **Access the web dashboard:**
   Open your browser and navigate to `http://localhost:5050` (or the configured host/port)

3. **Monitor devices:**
   - View real-time status of access points, switches, and cameras
   - Check event logs for device state changes
   - Monitor device statistics and health

## Architecture

- **app.py**: Main Flask web application and API endpoints
- **config.py**: Centralized configuration management
- **wlc_snmp.py**: WLC and access point monitoring via SNMP
- **switch_monitor.py**: Network switch monitoring via SNMP
- **camera_scanner.py**: IP camera detection via network scanning
- **templates/**: HTML templates for the web interface
- **static/**: CSS and JavaScript files for the web interface

## Database

The application uses SQLite databases to store:
- Access point information and events
- Switch status and events
- Camera detection results

Database files are automatically created in the application directory (Windows) or `/opt/wlc-monitor/` (Linux).

## Error Handling

The application includes comprehensive error handling:
- Configuration validation on startup
- Network timeout handling
- Database connection management
- Graceful degradation when devices are unreachable

## Security Considerations

- Store SNMP community strings securely in the `.env` file
- Restrict file permissions on the `.env` file: `chmod 600 .env`
- Consider using SNMP v3 for enhanced security
- Run the application with minimal required privileges

## Troubleshooting

1. **Configuration errors**: Check the console output for configuration validation errors
2. **Network connectivity**: Verify SNMP access to devices
3. **Database issues**: Check file permissions and disk space
4. **Performance**: Adjust thread counts based on system capabilities

For more detailed troubleshooting, check the application logs and ensure all required environment variables are properly configured.
