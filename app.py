from flask import Flask, render_template, jsonify
import sqlite3
from datetime import datetime, timedelta
import threading
import time
import os
import sys
import wlc_snmp
from wlc_snmp import connect_wlc_snmpv3, create_database, db_worker, db_queue, get_total_clients
from switch_monitor import (
    get_latest_switch_data,
    get_switch_events,
    init_database as init_switch_db,
    main as switch_monitor_main
)
from camera_scanner import get_camera_count, scan_network
import asyncio
import config

app = Flask(__name__)

def get_db_connection():
    # Create directory if it doesn't exist (Windows)
    if sys.platform == 'win32':
        os.makedirs(os.path.dirname(config.ACCESS_POINTS_DB_PATH), exist_ok=True)

    conn = sqlite3.connect(config.ACCESS_POINTS_DB_PATH, timeout=config.WLC_DB_TIMEOUT)
    conn.row_factory = sqlite3.Row
    return conn

def background_data_collection():
    while True:
        try:
            connect_wlc_snmpv3(config.WLC_HOST, config.WLC_COMMUNITY)
            time.sleep(60)  # Wait 60 seconds before next collection
        except Exception as e:
            print(f"Error in background collection: {str(e)}")
            time.sleep(5)  # Wait 5 seconds before retrying on error

def init_database():
    # Initialize the database and create tables
    create_database()

def background_camera_scan():
    while True:
        try:
            asyncio.run(scan_network())
            time.sleep(300)  # Scan every 5 minutes
        except Exception as e:
            print(f"Error in camera scan: {str(e)}")
            time.sleep(5)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/access-points')
def get_access_points():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get total clients
        total_clients = get_total_clients(config.WLC_HOST, config.WLC_COMMUNITY)
        
        # Add debug logging
        print("Fetching AP data...")
        
        cursor.execute('''
            WITH LatestAPs AS (
                SELECT ap_name, model, ip_address, last_seen,
                       ROW_NUMBER() OVER (PARTITION BY ap_name ORDER BY last_seen DESC) as rn
                FROM access_points
            )
            SELECT ap_name, model, ip_address, last_seen,
                   ROUND((JULIANDAY('now', 'localtime') - JULIANDAY(last_seen)) * 24 * 60, 1) as minutes_ago
            FROM LatestAPs
            WHERE rn = 1
            ORDER BY ap_name
        ''')
        
        columns = [column[0] for column in cursor.description]
        results = []
        stats = {
            'total': 0,
            'online': 0,
            'offline': 0,
            'models': {}
        }
        
        rows = cursor.fetchall()
        print(f"Found {len(rows)} APs")  # Debug log
        
        for row in rows:
            result = dict(zip(columns, row))
            minutes_ago = float(result['minutes_ago'])
            current_status = 'down' if minutes_ago > 2 else 'up'
            result['status'] = current_status
            
            # Update statistics
            stats['total'] += 1
            if current_status == 'up':
                stats['online'] += 1
            else:
                stats['offline'] += 1
            
            # Count by model
            model = result['model']
            if model not in stats['models']:
                stats['models'][model] = {'total': 0, 'online': 0, 'offline': 0}
            stats['models'][model]['total'] += 1
            if current_status == 'up':
                stats['models'][model]['online'] += 1
            else:
                stats['models'][model]['offline'] += 1
            
            results.append(result)

        conn.close()
        return jsonify({
            'aps': results, 
            'stats': stats,
            'total_clients': total_clients
        })
    except Exception as e:
        print(f"Error in get_access_points: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return jsonify({'error': str(e)}), 500

@app.route('/api/events')
def get_events():
    conn = None
    try:
        # Get AP events
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Modified query to get only unique events within the last hour
        cursor.execute('''
            WITH RankedEvents AS (
                SELECT 
                    ap_name as device_name,
                    'AP' as device_type,
                    event_type,
                    details,
                    event_time,
                    strftime('%H:%M:%S', event_time, 'localtime') as formatted_time,
                    ROW_NUMBER() OVER (
                        PARTITION BY ap_name, event_type, details 
                        ORDER BY event_time DESC
                    ) as rn
                FROM events
                WHERE event_time >= datetime('now', '-1 hour')
            )
            SELECT 
                device_name,
                device_type,
                event_type,
                details,
                formatted_time as event_time
            FROM RankedEvents
            WHERE rn = 1
            ORDER BY event_time DESC
        ''')
        
        ap_events = [dict(zip([col[0] for col in cursor.description], row)) 
                    for row in cursor.fetchall()]
        conn.close()

        # Get switch events - assuming similar structure for switch events
        switch_events = get_switch_events()
        
        # Convert switch events to match AP event format
        formatted_switch_events = []
        seen_switch_events = set()  # Track unique switch events
        
        for event in switch_events:
            # Create a unique key for this event
            event_key = (event['switch_ip'], event['event_type'], event['details'])
            
            if event_key not in seen_switch_events:
                formatted_switch_events.append({
                    'device_name': event['switch_ip'],
                    'device_type': 'Switch',
                    'event_type': event['event_type'],
                    'event_time': event['event_time'],
                    'details': event['details']
                })
                seen_switch_events.add(event_key)

        # Combine and sort all events by time (most recent first)
        all_events = ap_events + formatted_switch_events
        all_events.sort(key=lambda x: x['event_time'], reverse=True)
        
        # Take only the most recent 10 events
        all_events = all_events[:10]
        
        return jsonify(all_events)
    except Exception as e:
        print(f"Error getting events: {str(e)}")
        if conn:
            conn.close()
        return jsonify({'error': str(e)}), 500

def record_event(ap_name, event_type, details):
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Store current time
        cursor.execute('''
            INSERT INTO events (ap_name, event_type, details, event_time)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', (ap_name, event_type, details))
        
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error recording event: {str(e)}")
        if conn:
            conn.close()

@app.route('/api/switches')
def get_switches():
    try:
        switches = get_latest_switch_data()
        events = get_switch_events()
        camera_count = get_camera_count()  # Get camera count
        
        stats = {
            'total': len(switches),
            'online': len([s for s in switches if "Not responding" not in s['status']]),
            'offline': len([s for s in switches if "Not responding" in s['status']]),
            'models': {}
        }
        
        # Calculate model statistics
        for switch in switches:
            model = switch['model']
            if model not in stats['models']:
                stats['models'][model] = {'total': 0, 'online': 0, 'offline': 0}
            
            stats['models'][model]['total'] += 1
            if "Not responding" not in switch['status']:
                stats['models'][model]['online'] += 1
            else:
                stats['models'][model]['offline'] += 1
        
        return jsonify({
            'switches': switches,
            'events': events,
            'stats': stats,
            'camera_count': camera_count  # Add camera count to response
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras')
def get_cameras():
    try:
        camera_count = get_camera_count()
        return jsonify({
            'total_cameras': camera_count
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize database before starting the application
    init_database()
    init_switch_db()
    
    # Start the database worker thread for wlc_snmp
    db_worker_thread = threading.Thread(target=db_worker, args=(db_queue,), daemon=True)
    db_worker_thread.start()
    
    # Start the AP data collection in a separate thread
    collector_thread = threading.Thread(target=background_data_collection, daemon=True)
    collector_thread.start()
    
    # Start the switch monitoring in a separate thread
    switch_collector = threading.Thread(target=switch_monitor_main, daemon=True)
    switch_collector.start()
    
    # Start the camera scanning in a separate thread
    camera_scanner_thread = threading.Thread(target=background_camera_scan, daemon=True)
    camera_scanner_thread.start()
    
    # Print configuration summary
    print("\n" + "="*50)
    print("MONITORING APPLICATION STARTING")
    print("="*50)
    config.print_config_summary()
    print("="*50 + "\n")

    # Start Flask application
    app.run(host=config.FLASK_HOST, port=config.FLASK_PORT)