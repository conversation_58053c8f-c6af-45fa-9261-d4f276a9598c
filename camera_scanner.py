import asyncio
import ipaddress
import time
from tqdm import tqdm
import multiprocessing
import sqlite3
from datetime import datetime
import os
import sys
import config

def init_database():
    """Initialize SQLite database and create tables if they don't exist"""
    conn = sqlite3.connect(config.NETWORK_DEVICES_DB_PATH)
    cursor = conn.cursor()
    
    # Create table for online devices
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS online_cameras (
            ip_address TEXT,
            last_seen timestamp,
            PRIMARY KEY (ip_address)
        )
    ''')
    
    conn.commit()
    return conn

def save_devices_to_db(online_devices, conn):
    """Save online devices to database"""
    cursor = conn.cursor()
    current_time = datetime.now()
    
    # Clear old records
    cursor.execute('DELETE FROM online_cameras')
    
    # Insert new records
    for ip in online_devices:
        cursor.execute('''
            INSERT INTO online_cameras (ip_address, last_seen)
            VALUES (?, ?)
        ''', (ip, current_time))
    
    conn.commit()

async def tcp_check(ip, port=80):
    """Check if TCP port is open"""
    try:
        _, writer = await asyncio.wait_for(
            asyncio.open_connection(str(ip), port), 
            timeout=0.5
        )
        writer.close()
        await writer.wait_closed()
        return str(ip)
    except:
        return None

async def scan_single_ip(ip, semaphore, pbar):
    async with semaphore:
        result = await tcp_check(ip)
        pbar.update(1)
        if result:
            pbar.write(f"Found camera: {result}")
        return result

async def scan_network(thread_count=None):
    # Use configured thread count if not specified
    if thread_count is None:
        thread_count = config.CAMERA_THREAD_COUNT

    # Initialize database
    db_conn = init_database()

    # Get scan ranges from configuration
    ranges = [ipaddress.IPv4Network(range_str) for range_str in config.CAMERA_SCAN_RANGES]

    all_ips = [ip for network in ranges for ip in network.hosts()]
    # Filter out excluded IPs
    all_ips = [ip for ip in all_ips if str(ip) not in config.CAMERA_EXCLUDED_IPS]
    total_ips = len(all_ips)
    
    print(f"\nStarting camera scan of {total_ips} IP addresses...")
    print(f"CPU Cores: {multiprocessing.cpu_count()}")
    print(f"Concurrent tasks: {thread_count}")
    
    semaphore = asyncio.Semaphore(thread_count)
    
    with tqdm(total=total_ips, unit='ip') as pbar:
        tasks = [scan_single_ip(ip, semaphore, pbar) for ip in all_ips]
        start_time = time.time()
        results = await asyncio.gather(*tasks)
    
    # Filter out None results and excluded IPs
    online_devices = [ip for ip in results if ip is not None and ip not in config.CAMERA_EXCLUDED_IPS]
    duration = time.time() - start_time
    
    # Save results to database
    save_devices_to_db(online_devices, db_conn)
    
    # Print statistics
    print(f"\nCamera Scan Statistics:")
    print(f"----------------")
    print(f"Total IPs scanned: {total_ips}")
    print(f"Online cameras: {len(online_devices)}")
    print(f"Scan duration: {duration:.2f} seconds")
    print(f"Speed: {total_ips/duration:.1f} IPs/second")
    
    # Close database connection
    db_conn.close()
    return online_devices

def get_camera_count():
    """Get the count of online cameras from the database"""
    try:
        conn = sqlite3.connect(config.NETWORK_DEVICES_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM online_cameras')
        count = cursor.fetchone()[0]
        conn.close()
        return count
    except Exception as e:
        print(f"Error getting camera count: {e}")
        return 0

def get_total_cameras():
    """Get the total number of cameras from the database"""
    try:
        # Initialize database if it doesn't exist
        init_database()

        conn = sqlite3.connect(config.NETWORK_DEVICES_DB_PATH)
        cursor = conn.cursor()
        
        # Create table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS online_cameras (
                ip_address TEXT,
                last_seen timestamp,
                PRIMARY KEY (ip_address)
            )
        ''')
        conn.commit()
        
        cursor.execute('SELECT COUNT(*) FROM online_cameras')
        total_cameras = cursor.fetchone()[0]
        conn.close()
        return total_cameras
    except Exception as e:
        print(f"Error getting total cameras: {e}")
        return 0

if __name__ == "__main__":
    print("Starting camera network scan...")
    asyncio.run(scan_network()) 