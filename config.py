"""
Configuration management for the monitoring application.
Loads environment variables from .env file and provides validation.
"""

import os
import sys
import ipaddress
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class ConfigError(Exception):
    """Raised when there's a configuration error"""
    pass

def validate_ip_address(ip_str, var_name):
    """Validate that a string is a valid IP address"""
    try:
        ipaddress.ip_address(ip_str)
        return ip_str
    except ValueError:
        raise ConfigError(f"Invalid IP address for {var_name}: {ip_str}")

def validate_network_range(network_str, var_name):
    """Validate that a string is a valid network range"""
    try:
        ipaddress.ip_network(network_str, strict=False)
        return network_str
    except ValueError:
        raise ConfigError(f"Invalid network range for {var_name}: {network_str}")

def parse_ip_list(ip_list_str, var_name):
    """Parse comma-separated IP addresses and validate them"""
    if not ip_list_str:
        return []
    
    ips = [ip.strip() for ip in ip_list_str.split(',') if ip.strip()]
    validated_ips = []
    
    for ip in ips:
        validated_ips.append(validate_ip_address(ip, var_name))
    
    return validated_ips

def parse_network_list(network_list_str, var_name):
    """Parse comma-separated network ranges and validate them"""
    if not network_list_str:
        return []
    
    networks = [net.strip() for net in network_list_str.split(',') if net.strip()]
    validated_networks = []
    
    for network in networks:
        validated_networks.append(validate_network_range(network, var_name))
    
    return validated_networks

# WLC Configuration
WLC_HOST = os.getenv('WLC_HOST')
if not WLC_HOST:
    raise ConfigError("WLC_HOST environment variable is required")
WLC_HOST = validate_ip_address(WLC_HOST, 'WLC_HOST')

WLC_COMMUNITY = os.getenv('WLC_COMMUNITY')
if not WLC_COMMUNITY:
    raise ConfigError("WLC_COMMUNITY environment variable is required")

WLC_DB_TIMEOUT = float(os.getenv('WLC_DB_TIMEOUT', '30.0'))

# Switch Configuration
SWITCH_COMMUNITY = os.getenv('SWITCH_COMMUNITY', 'public')

SWITCH_IPS_STR = os.getenv('SWITCH_IPS')
if not SWITCH_IPS_STR:
    raise ConfigError("SWITCH_IPS environment variable is required")
SWITCH_IPS = parse_ip_list(SWITCH_IPS_STR, 'SWITCH_IPS')

# Camera Scanner Configuration
CAMERA_SCAN_RANGES_STR = os.getenv('CAMERA_SCAN_RANGES', '**********/24,**********/24')
CAMERA_SCAN_RANGES = parse_network_list(CAMERA_SCAN_RANGES_STR, 'CAMERA_SCAN_RANGES')

CAMERA_EXCLUDED_IPS_STR = os.getenv('CAMERA_EXCLUDED_IPS', 
    '************,************,************,************,************,************,************')
CAMERA_EXCLUDED_IPS = set(parse_ip_list(CAMERA_EXCLUDED_IPS_STR, 'CAMERA_EXCLUDED_IPS'))

CAMERA_THREAD_COUNT = int(os.getenv('CAMERA_THREAD_COUNT', '50'))

# Flask Configuration
FLASK_HOST = os.getenv('FLASK_HOST')
if not FLASK_HOST:
    # Use localhost for Windows, specific IP for Linux (maintaining existing logic)
    FLASK_HOST = '127.0.0.1' if sys.platform == 'win32' else '***********'

FLASK_PORT = int(os.getenv('FLASK_PORT', '5050'))

# Database Configuration
# Keep existing OS-dependent logic for database paths
if sys.platform == 'win32':
    ACCESS_POINTS_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'access_points.db')
    SWITCH_MONITORING_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'switch_monitoring.db')
    NETWORK_DEVICES_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'network_devices.db')
else:
    ACCESS_POINTS_DB_PATH = '/opt/wlc-monitor/access_points.db'
    SWITCH_MONITORING_DB_PATH = '/opt/wlc-monitor/switch_monitoring.db'
    NETWORK_DEVICES_DB_PATH = '/opt/wlc-monitor/network_devices.db'

# Allow override via environment variables if needed
ACCESS_POINTS_DB_PATH = os.getenv('ACCESS_POINTS_DB_PATH', ACCESS_POINTS_DB_PATH)
SWITCH_MONITORING_DB_PATH = os.getenv('SWITCH_MONITORING_DB_PATH', SWITCH_MONITORING_DB_PATH)
NETWORK_DEVICES_DB_PATH = os.getenv('NETWORK_DEVICES_DB_PATH', NETWORK_DEVICES_DB_PATH)

def print_config_summary():
    """Print a summary of the loaded configuration (without sensitive data)"""
    print("Configuration Summary:")
    print(f"  WLC Host: {WLC_HOST}")
    print(f"  WLC Community: {'*' * len(WLC_COMMUNITY)}")  # Hide sensitive data
    print(f"  WLC DB Timeout: {WLC_DB_TIMEOUT}s")
    print(f"  Switch Community: {'*' * len(SWITCH_COMMUNITY)}")  # Hide sensitive data
    print(f"  Switch IPs: {len(SWITCH_IPS)} switches configured")
    print(f"  Camera Scan Ranges: {CAMERA_SCAN_RANGES}")
    print(f"  Camera Excluded IPs: {len(CAMERA_EXCLUDED_IPS)} IPs excluded")
    print(f"  Camera Thread Count: {CAMERA_THREAD_COUNT}")
    print(f"  Flask Host: {FLASK_HOST}")
    print(f"  Flask Port: {FLASK_PORT}")
    print(f"  Access Points DB: {ACCESS_POINTS_DB_PATH}")
    print(f"  Switch Monitoring DB: {SWITCH_MONITORING_DB_PATH}")
    print(f"  Network Devices DB: {NETWORK_DEVICES_DB_PATH}")

if __name__ == "__main__":
    # Test configuration loading
    try:
        print_config_summary()
        print("\nConfiguration loaded successfully!")
    except ConfigError as e:
        print(f"Configuration Error: {e}")
        sys.exit(1)
