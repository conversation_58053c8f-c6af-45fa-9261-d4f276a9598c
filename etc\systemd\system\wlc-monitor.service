[Unit]
Description=WLC Monitor Service
After=network.target

[Service]
User=root
WorkingDirectory=/opt/wlc-monitor
Environment="PATH=/opt/wlc-monitor/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="PYTHONPATH=/opt/wlc-monitor"
ExecStart=/opt/wlc-monitor/venv/bin/python3 /opt/wlc-monitor/app.py
Restart=always
RestartSec=5
StandardOutput=append:/var/log/wlc-monitor.log
StandardError=append:/var/log/wlc-monitor.error.log

[Install]
WantedBy=multi-user.target 