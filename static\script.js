const FADE_DURATION = 200; // Reduced from 300 to 200 milliseconds for quicker transition
let countdown = 30;  // Countdown in seconds
let refreshInterval = 30;  // Refresh interval in seconds
let updateInProgress = false;  // Flag to prevent multiple simultaneous updates

function fadeOut(element) {
    return new Promise(resolve => {
        element.style.transition = `opacity ${FADE_DURATION}ms`;
        element.style.opacity = '0';
        setTimeout(() => {
            resolve();
        }, FADE_DURATION);
    });
}

function fadeIn(element) {
    element.style.opacity = '0';
    element.style.transition = `opacity ${FADE_DURATION}ms`;
    // Trigger reflow
    element.offsetHeight;
    element.style.opacity = '1';
}

async function updateAll() {
    if (updateInProgress) {
        return;
    }
    
    updateInProgress = true;

    try {
        // Fetch all data without fading out the old content first
        const [apData, switchData, eventData] = await Promise.all([
            fetch('/api/access-points').then(response => response.json()),
            fetch('/api/switches').then(response => response.json()),
            fetch('/api/events').then(response => response.json())
        ]);

        // Only after we have the new data, fade out the old content
        const contentAreas = [
            document.getElementById('combined-alerts-container'),
            document.getElementById('combined-events-container'),
            document.getElementById('ap-grid'),
            document.getElementById('switch-grid')
        ];

        await Promise.all(contentAreas.map(element => fadeOut(element)));

        // Clear existing stats containers
        const existingStats = document.querySelectorAll('.stats-container');
        existingStats.forEach(container => container.remove());

        // Update content
        updateAPTable(apData);
        updateSwitchTable(switchData);
        updateAlerts(apData, switchData);
        updateEvents(eventData);
        updateLastUpdateTime();

        // Fade in all content areas
        contentAreas.forEach(element => fadeIn(element));

        countdown = refreshInterval;
    } catch (error) {
        console.error('Error:', error);
        // In case of error, make sure content is visible
        const contentAreas = [
            document.getElementById('combined-alerts-container'),
            document.getElementById('combined-events-container'),
            document.getElementById('ap-grid'),
            document.getElementById('switch-grid')
        ];
        contentAreas.forEach(element => {
            if (element) element.style.opacity = '1';
        });
    } finally {
        updateInProgress = false;
    }
}

function updateAPTable(data) {
    const apGrid = document.getElementById('ap-grid');
    const newContent = document.createElement('div');
    
    // Calculate stats excluding empty model strings
    const stats = {
        total: data.aps.length,
        online: data.aps.filter(ap => ap.minutes_ago <= 2).length,
        offline: data.aps.filter(ap => ap.minutes_ago > 2).length,
        models: {}
    };

    // Count models, excluding empty strings and null/undefined values
    data.aps.forEach(ap => {
        // Skip if model is null, undefined, empty string, or only whitespace
        if (!ap.model || typeof ap.model !== 'string' || ap.model.trim() === '') {
            return;  // Skip this iteration
        }
        
        const model = ap.model.trim();  // Trim any whitespace
        if (!stats.models[model]) {
            stats.models[model] = {
                total: 0,
                online: 0,
                offline: 0
            };
        }
        stats.models[model].total++;
        if (ap.minutes_ago <= 2) {
            stats.models[model].online++;
        } else {
            stats.models[model].offline++;
        }
    });
    
    // Update statistics
    const statsHtml = `
        <div class="stats-container mb-3">
            <div class="row g-2">
                <div class="col-3">
                    <div class="stats-card">
                        <div class="stats-value">${stats.total}</div>
                        <div class="stats-label">APs</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-success text-white">
                        <div class="stats-value">${stats.online}</div>
                        <div class="stats-label">Online</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-danger text-white">
                        <div class="stats-value">${stats.offline}</div>
                        <div class="stats-label">Offline</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-info text-white">
                        <div class="stats-value">${data.total_clients || 0}</div>
                        <div class="stats-label">Clients</div>
                    </div>
                </div>
            </div>
            ${Object.keys(stats.models).length > 0 ? `
                <div class="models-stats mt-3">
                    <h6 class="mb-2">By Model:</h6>
                    <div class="row g-2">
                        ${Object.entries(stats.models).map(([model, counts]) => `
                            <div class="col-md-6 col-lg-4">
                                <div class="model-stat-card">
                                    <div class="model-name">${model}</div>
                                    <div class="model-counts">
                                        Total: ${counts.total} (${counts.online} Up, ${counts.offline} Down)
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;
    
    const existingStats = document.querySelectorAll('.stats-container');
    existingStats.forEach(container => container.remove());
    apGrid.insertAdjacentHTML('beforebegin', statsHtml);
    
    // Sort data to show down APs first, then sort by name
    data.aps.sort((a, b) => {
        if (a.status !== b.status) {
            return a.status === 'down' ? -1 : 1;
        }
        return a.ap_name.localeCompare(b.ap_name);
    });
    
    data.aps.forEach(ap => {
        const apItem = document.createElement('div');
        const status = ap.minutes_ago > 2 ? 'down' : 'up';
        apItem.className = `ap-item ${status}`;
        
        const tooltipContent = `
            Model: ${ap.model}<br>
            IP: ${ap.ip_address}<br>
            Status: ${status.toUpperCase()}<br>
            Last seen: ${formatLastSeen(ap.last_seen, ap.minutes_ago)}
        `;
        
        apItem.innerHTML = `
            <div class="status-indicator ${status === 'up' ? 'online' : 'offline'}"></div>
            <i class="fas fa-wifi ap-icon"></i>
            <p class="ap-name">${ap.ap_name}</p>
            <div class="ap-tooltip">${tooltipContent}</div>
        `;
        
        newContent.appendChild(apItem);
    });
    
    apGrid.innerHTML = newContent.innerHTML;
}

function updateAlerts(apData, switchData) {
    const alertsContainer = document.getElementById('combined-alerts-container');
    alertsContainer.innerHTML = '';
    
    let allAlerts = [];
    
    // Add AP alerts
    const downAPs = apData.aps.filter(ap => ap.minutes_ago > 2).map(ap => ({
        name: ap.ap_name,
        type: 'AP',
        model: ap.model,
        ip: ap.ip_address,
        timestamp: ap.last_seen,
        minutes_ago: ap.minutes_ago
    }));
    
    // Add Switch alerts
    const downSwitches = switchData.switches.filter(sw => sw.status.includes("Not responding")).map(sw => ({
        name: sw.ip_address,
        type: 'Switch',
        model: sw.model,
        status: sw.status,
        timestamp: sw.timestamp
    }));
    
    // Combine and sort all alerts
    allAlerts = [...downAPs, ...downSwitches].sort((a, b) => {
        const timeA = new Date(a.timestamp);
        const timeB = new Date(b.timestamp);
        return timeB - timeA;  // Most recent first
    });
    
    // Show only the 4 most recent alerts
    allAlerts.slice(0, 4).forEach(alert => {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert-item';
        
        if (alert.type === 'AP') {
            alertDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-baseline">
                    <strong><i class="fas fa-wifi me-2"></i>${alert.name}</strong>
                    <small class="text-muted">${formatLastSeen(alert.timestamp, alert.minutes_ago)}</small>
                </div>
                <div class="text-muted">
                    ${alert.model} | ${alert.ip}
                </div>
            `;
        } else {
            alertDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-baseline">
                    <strong><i class="fas fa-network-wired me-2"></i>${alert.name}</strong>
                    <small class="text-muted">${formatLastSeen(alert.timestamp)}</small>
                </div>
                <div class="text-muted">
                    ${alert.model} | ${alert.status}
                </div>
            `;
        }
        
        alertsContainer.appendChild(alertDiv);
    });
    
    if (allAlerts.length === 0) {
        alertsContainer.innerHTML = `
            <div class="text-center py-4 status-message">
                <i class="fas fa-check-circle text-success pulse-success"></i>
                <p class="text-success mb-0 mt-2 status-text fade-in">All Systems Operational</p>
            </div>
        `;
    }
}

function updateEvents(events) {
    const eventsContainer = document.getElementById('combined-events-container');
    eventsContainer.innerHTML = '';
    
    if (!events || events.length === 0) {
        eventsContainer.innerHTML = `
            <div class="text-center py-4 status-message">
                <i class="fas fa-check-circle text-info pulse-info"></i>
                <p class="text-info mb-0 mt-2 status-text fade-in">No Recent Events</p>
            </div>
        `;
        return;
    }
    
    // Show only the most recent events
    events.slice(0, 10).forEach(event => {  // Show up to 10 events
        const eventDiv = document.createElement('div');
        eventDiv.className = 'event-item';
        
        // Determine icon based on device type
        const deviceIcon = event.device_type === 'AP' ? 'wifi' : 'network-wired';
        
        // Format the event details
        let eventDetails = '';
        if (event.device_type === 'AP') {
            eventDetails = event.details || 'AP is back online';  // Default text if details missing
        } else {
            eventDetails = event.details || 'Switch status changed';  // Default text for switches
        }
        
        eventDiv.innerHTML = `
            <div class="d-flex align-items-baseline">
                <span class="me-2">
                    <i class="fas fa-${deviceIcon} ${event.event_type === 'up' ? 'text-success' : 'text-danger'}"></i>
                </span>
                <div class="flex-grow-1">
                    <strong>${event.device_name}</strong>
                    <span class="text-muted ms-2">${eventDetails}</span>
                </div>
                <small class="text-muted ms-2">${event.event_time}</small>
            </div>
        `;
        
        eventsContainer.appendChild(eventDiv);
    });
}

function formatLastSeen(timestamp, minutesAgo) {
    if (minutesAgo < 1) {
        return 'Just now';
    }
    if (minutesAgo === 1) {
        return '1 minute ago';
    }
    if (minutesAgo < 60) {
        return `${Math.floor(minutesAgo)} minutes ago`;
    }
    if (minutesAgo < 120) {
        return '1 hour ago';
    }
    if (minutesAgo < 1440) {
        return `${Math.floor(minutesAgo / 60)} hours ago`;
    }
    if (minutesAgo < 2880) {
        return '1 day ago';
    }
    if (minutesAgo < 10080) { // 7 days
        return `${Math.floor(minutesAgo / 1440)} days ago`;
    }
    
    // For timestamps older than 7 days, show the actual date
    return new Date(timestamp).toLocaleString();
}

function updateLastUpdateTime() {
    const element = document.getElementById('last-update');
    element.textContent = `Last updated: ${new Date().toLocaleString()} (Next refresh in ${countdown}s)`;
}

function startCountdown() {
    let countdownInterval = setInterval(() => {
        countdown--;
        if (countdown <= 0) {
            updateAll();
        }
        updateLastUpdateTime();
    }, 1000);
}

// Add new function to update statistics
function updateStats(stats) {
    const statsHtml = `
        <div class="stats-container mb-3">
            <div class="row g-2">
                <div class="col-4">
                    <div class="stats-card">
                        <div class="stats-value">${stats.total}</div>
                        <div class="stats-label">APs</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="stats-card bg-success text-white">
                        <div class="stats-value">${stats.online}</div>
                        <div class="stats-label">Online</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="stats-card bg-danger text-white">
                        <div class="stats-value">${stats.offline}</div>
                        <div class="stats-label">Offline</div>
                    </div>
                </div>
            </div>
            <div class="models-stats mt-3">
                <h6 class="mb-2">By Model:</h6>
                <div class="row g-2">
                    ${Object.entries(stats.models).map(([model, counts]) => `
                        <div class="col-md-6 col-lg-4">
                            <div class="model-stat-card">
                                <div class="model-name">${model}</div>
                                <div class="model-counts">
                                    Total: ${counts.total} (${counts.online} Up, ${counts.offline} Down)
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    const apGrid = document.getElementById('ap-grid');
    apGrid.insertAdjacentHTML('beforebegin', statsHtml);
}

// Add new function for switch table
function updateSwitchTable(data) {
    const switchGrid = document.getElementById('switch-grid');
    switchGrid.innerHTML = '';
    
    console.log('Raw switch data:', data.switches); // Debug log
    
    // Calculate stats excluding empty model strings
    const stats = {
        total: data.switches.length,
        online: data.switches.filter(s => !s.status.includes("Not responding")).length,
        offline: data.switches.filter(s => s.status.includes("Not responding")).length,
        models: {}
    };

    // Count models, excluding empty strings and null/undefined values
    data.switches.forEach(sw => {
        console.log('Processing switch model:', sw.model, 'Type:', typeof sw.model); // Debug log
        
        // Skip if model is null, undefined, empty string, or only whitespace
        if (!sw.model || typeof sw.model !== 'string' || sw.model.trim() === '') {
            console.log('Skipping invalid model:', sw.model); // Debug log
            return;  // Skip this iteration
        }
        
        const model = sw.model.trim();  // Trim any whitespace
        console.log('Valid model found:', model); // Debug log
        
        if (!stats.models[model]) {
            stats.models[model] = {
                total: 0,
                online: 0,
                offline: 0
            };
        }
        stats.models[model].total++;
        if (sw.status.includes("Not responding")) {
            stats.models[model].offline++;
        } else {
            stats.models[model].online++;
        }
    });
    
    console.log('Final stats:', stats); // Debug log
    
    // Add camera stats section after the switch stats
    const statsHtml = `
        <div class="stats-container mb-3">
            <div class="row g-2">
                <div class="col-3">
                    <div class="stats-card">
                        <div class="stats-value">${stats.total}</div>
                        <div class="stats-label">Switches</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-success text-white">
                        <div class="stats-value">${stats.online}</div>
                        <div class="stats-label">Online</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-danger text-white">
                        <div class="stats-value">${stats.offline}</div>
                        <div class="stats-label">Offline</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="stats-card bg-info text-white">
                        <div class="stats-value">${data.camera_count || 0}</div>
                        <div class="stats-label">Cameras</div>
                    </div>
                </div>
            </div>
            ${Object.keys(stats.models).length > 0 ? `
                <div class="models-stats mt-3">
                    <h6 class="mb-2">By Model:</h6>
                    <div class="row g-2">
                        ${Object.entries(stats.models).map(([model, counts]) => `
                            <div class="col-md-6 col-lg-4">
                                <div class="model-stat-card">
                                    <div class="model-name">${model}</div>
                                    <div class="model-counts">
                                        Total: ${counts.total} (${counts.online} Up, ${counts.offline} Down)
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;
    
    switchGrid.insertAdjacentHTML('beforebegin', statsHtml);
    
    // Sort switches to show offline ones first
    data.switches.sort((a, b) => {
        const aOffline = a.status.includes("Not responding");
        const bOffline = b.status.includes("Not responding");
        if (aOffline !== bOffline) {
            return aOffline ? -1 : 1;
        }
        return a.ip_address.localeCompare(b.ip_address);
    });
    
    // Create switch grid items
    data.switches.forEach(switchData => {
        const switchItem = document.createElement('div');
        const isOnline = !switchData.status.includes("Not responding");
        switchItem.className = `ap-item ${isOnline ? 'up' : 'down'}`;
        
        const tooltipContent = `
            Model: ${switchData.model}<br>
            Image: ${switchData.image}<br>
            Stack: ${switchData.status}<br>
            Last seen: ${formatLastSeen(switchData.timestamp)}
        `;
        
        // Check if it's a stacked switch and create stack status text
        let stackStatus = '';
        if (switchData.total_stack_members > 1) {
            stackStatus = `<p class="stack-status">${switchData.online_stack_members}/${switchData.total_stack_members} online</p>`;
        }
        
        switchItem.innerHTML = `
            <div class="status-indicator ${isOnline ? 'online' : 'offline'}"></div>
            <i class="fas fa-network-wired"></i>
            <p class="ap-name">${switchData.ip_address}</p>
            ${stackStatus}
            <div class="ap-tooltip">${tooltipContent}</div>
        `;
        
        switchGrid.appendChild(switchItem);
    });
}

// Add CSS transitions to your style.css
function addStylesheet() {
    const style = document.createElement('style');
    style.textContent = `
        .ap-item, .alert-item, .event-item {
            transition: opacity ${FADE_DURATION}ms ease-in-out;
        }
        
        .stats-container {
            transition: opacity ${FADE_DURATION}ms ease-in-out;
        }
        
        #ap-grid, #switch-grid, 
        #combined-alerts-container, 
        #combined-events-container {
            transition: opacity ${FADE_DURATION}ms ease-in-out;
        }
    `;
    document.head.appendChild(style);
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    addStylesheet();
    updateAll();
    startCountdown();
});

// Update the refresh button click handler in your HTML
// <button onclick="updateAll()" class="btn btn-outline-light me-2"> 