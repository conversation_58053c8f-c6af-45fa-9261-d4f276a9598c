body {
    background-color: #f5f5f5;
}

.card {
    margin-bottom: 20px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.ap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    padding: 10px;
}

.ap-item {
    position: relative;
    background-color: #fff;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ddd;
    margin-top: 20px;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.ap-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.ap-item.down {
    background-color: #fff3f3;
    border-color: #dc3545;
}

.ap-item.up {
    background-color: #f8fff8;
    border-color: #28a745;
}

.ap-icon {
    font-size: 14px;
    margin-bottom: 6px;
    color: #28a745;
}

.ap-item.down .ap-icon {
    color: #dc3545;
}

.ap-name {
    font-size: 11px;
    font-weight: bold;
    margin: 0;
    word-break: break-word;
}

.ap-tooltip {
    position: absolute;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    width: 180px;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s;
    text-align: left;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
}

.ap-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0,0,0,0.8) transparent transparent transparent;
}

.ap-item:hover .ap-tooltip {
    visibility: visible;
    opacity: 1;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-up {
    background-color: #28a745;
}

.status-down {
    background-color: #dc3545;
}

.alert-item {
    padding: 6px 8px;
    margin-bottom: 6px;
    border-radius: 4px;
    background-color: #fff3f3;
    border-left: 3px solid #dc3545;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    font-size: 0.85em;
}

.alert-item strong {
    color: #dc3545;
    font-size: 0.9em;
}

.alert-item .text-muted {
    font-size: 0.8em;
}

.event-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.event-item:last-child {
    border-bottom: none;
}

.event-item .fas {
    width: 16px;
    text-align: center;
}

.event-item strong {
    font-size: 12px;
}

.event-item .text-muted {
    font-size: 11px;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* Add scrollable containers for alerts and events */
#alerts-container, #events-container {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

#events-container {
    max-height: 400px;
}

/* Add custom scrollbar styling */
#alerts-container::-webkit-scrollbar,
#events-container::-webkit-scrollbar {
    width: 6px;
}

#alerts-container::-webkit-scrollbar-thumb,
#events-container::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
}

#alerts-container::-webkit-scrollbar-track,
#events-container::-webkit-scrollbar-track {
    background: #f5f5f5;
}

/* Add this to your existing CSS */
.navbar-logo {
    height: 40px;
    width: auto;
    filter: none;
}

/* Optional: Add hover effect */
.navbar-logo:hover {
    filter: brightness(0) invert(0.9);
}

/* Update navbar and color scheme */
.navbar {
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0.5rem 1rem;
}

.navbar-brand {
    color: #333 !important;
}

.navbar-text {
    color: #666 !important;
}

/* Update the refresh button to match the new color scheme */
.btn-outline-light {
    color: #333 !important;
    border-color: #ddd !important;
    background-color: transparent !important;
}

.btn-outline-light:hover {
    color: #000 !important;
    background-color: #f8f9fa !important;
    border-color: #ccc !important;
}

/* Optional: Update other elements to match the new color scheme */
.card-header.bg-danger {
    background-color: #dc3545 !important;
}

.card-header.bg-info {
    background-color: #0dcaf0 !important;
}

/* Add/update responsive styles */
.container-fluid {
    padding: 0 15px;
    max-width: 1800px; /* Prevent too wide layouts on very large screens */
    margin: 0 auto;
}

/* Responsive navbar */
.navbar {
    padding: 0.5rem;
}

.navbar-logo {
    height: 35px; /* Slightly smaller on mobile */
    width: auto;
}

/* Responsive grid */
.ap-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); /* Smaller minimum size */
    gap: 6px;
    padding: 8px;
}

/* Media queries for different screen sizes */
@media (max-width: 768px) {
    .row {
        margin: 0; /* Remove default margins on mobile */
    }
    
    .col-md-8, .col-md-4 {
        padding: 0; /* Remove padding on mobile */
    }

    .navbar-brand {
        font-size: 1rem; /* Smaller title on mobile */
    }

    .navbar-text {
        display: none; /* Hide last update text on mobile */
    }

    .btn-outline-light {
        padding: 0.25rem 0.5rem; /* Smaller button on mobile */
        font-size: 0.875rem;
    }

    .card {
        margin-bottom: 10px; /* Less spacing between cards on mobile */
    }

    .card-header h5 {
        font-size: 1rem; /* Smaller card headers on mobile */
    }

    #alerts-container, #events-container {
        max-height: 250px; /* Shorter containers on mobile */
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .ap-grid {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    }

    .navbar-logo {
        height: 38px;
    }
}

@media (min-width: 1025px) {
    .ap-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

/* Fix for very small screens */
@media (max-width: 375px) {
    .navbar {
        flex-wrap: nowrap;
    }

    .navbar-brand {
        font-size: 0.9rem;
    }

    .ap-grid {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    }

    .ap-name {
        font-size: 10px;
    }
}

/* Statistics styling */
.stats-container {
    padding: 15px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

.stats-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    height: 100%;
    min-height: 90px;  /* Set minimum height for consistency */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 13px;
    color: inherit;
    margin: 0;
    line-height: 1.2;
}

/* Make all colored stat cards consistent */
.stats-card.bg-success,
.stats-card.bg-danger,
.stats-card.bg-info,
.stats-card.bg-primary {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card.bg-success .stats-value,
.stats-card.bg-danger .stats-value,
.stats-card.bg-info .stats-value,
.stats-card.bg-primary .stats-value {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.stats-card.bg-success .stats-label,
.stats-card.bg-danger .stats-label,
.stats-card.bg-info .stats-label,
.stats-card.bg-primary .stats-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
}

/* Ensure consistent spacing in the grid */
.row.g-2 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
}

.col-3 {
    padding: var(--bs-gutter-x);
}

.models-stats {
    border-top: 1px solid #eee;
    padding-top: 8px;
    margin-top: 8px;
}

.models-stats h6 {
    font-size: 12px;
    margin-bottom: 6px;
}

.model-stat-card {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 6px 8px;
    margin-bottom: 6px;
}

.model-name {
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 2px;
}

.model-counts {
    font-size: 11px;
    color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-value {
        font-size: 16px;
    }
    
    .stats-label {
        font-size: 10px;
    }
    
    .model-name {
        font-size: 11px;
    }
    
    .model-counts {
        font-size: 10px;
    }
}

/* Add to your existing CSS */
.switch-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    padding: 10px;
}

.ap-item .fa-network-wired {
    font-size: 14px;
    margin-bottom: 6px;
    color: #28a745;
}

.ap-item.down .fa-network-wired {
    color: #dc3545;
}

/* Add these styles */
.stack-status {
    font-size: 10px;
    margin: 2px 0 0 0;
    color: #666;
}

.down .stack-status {
    color: #dc3545;
}

/* Update alert and event containers */
#combined-alerts-container, #combined-events-container {
    max-height: 150px;
    overflow-y: auto;
}

/* Update the card headers to be more compact */
.card-header {
    padding: 0.5rem 1rem;
}

.card-header h5 {
    font-size: 0.95rem;
    margin: 0;
}

/* Update the card body padding */
.card-body {
    padding: 0.75rem;
}

/* Add some spacing between the alert/event sections and the grids */
.row.mb-4 {
    margin-bottom: 1rem !important;
}

/* Remove the old navbar styles and add these new styles */
.refresh-bar {
    background-color: #f8f9fa;
    padding: 4px 8px;
    font-size: 11px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.btn-sm {
    padding: 2px 6px;
    font-size: 11px;
}

.text-muted {
    font-size: 11px;
}

/* Remove these old navbar styles if they exist */
.navbar, .navbar-logo {
    display: none;
}

/* Update container padding */
.container-fluid {
    padding: 0 10px;
}

/* Add these styles for the blinking indicator */
.status-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.status-indicator.online {
    background-color: #28a745;
    animation: blink-green 2s infinite;
}

.status-indicator.offline {
    background-color: #dc3545;
    animation: blink-red 2s infinite;
}

@keyframes blink-green {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}

@keyframes blink-red {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}

/* Update ap-item to accommodate the indicator */
.ap-item {
    position: relative;  /* Make sure this is set */
    /* ... rest of existing ap-item styles ... */
}

/* Add these styles for the camera count box */
.stats-card.bg-primary {
    background-color: #007bff !important;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card.bg-primary .stats-value {
    font-size: 24px;
    font-weight: bold;
}

.stats-card.bg-primary .stats-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
}

/* Update the camera count box style */
.stats-card.bg-info {
    background-color: #17a2b8 !important;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card.bg-info .stats-value {
    font-size: 24px;
    font-weight: bold;
}

.stats-card.bg-info .stats-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
}

/* Update event history styling */
.event-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.event-item:last-child {
    border-bottom: none;
}

.event-item .fas {
    width: 16px;
    text-align: center;
}

.event-item strong {
    font-size: 12px;
}

.event-item .text-muted {
    font-size: 11px;
}

#combined-events-container {
    max-height: 300px;
    overflow-y: auto;
}

/* Add scrollbar styling */
#combined-events-container::-webkit-scrollbar {
    width: 4px;
}

#combined-events-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#combined-events-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

#combined-events-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Add these styles at the end of your CSS file */
.text-center .fas {
    opacity: 0.8;
    margin-bottom: 8px;
}

.text-center .text-success,
.text-center .text-info {
    font-size: 14px;
    font-weight: 500;
}

#combined-alerts-container .text-center,
#combined-events-container .text-center {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    margin: 10px 0;
}

#combined-alerts-container .text-center .fas {
    color: #198754;  /* Bootstrap success color */
}

#combined-events-container .text-center .fas {
    color: #0dcaf0;  /* Bootstrap info color */
}

/* Update and add these styles at the end of your CSS file */
.status-message {
    padding: 20px !important;
}

.status-message .fas {
    font-size: 3rem !important;
    margin-bottom: 15px;
    opacity: 0.9;
}

.status-text {
    font-size: 1.25rem !important;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Pulse animation for success icon */
.pulse-success {
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0% {
        transform: scale(1);
        text-shadow: 0 0 0 rgba(40, 167, 69, 0);
    }
    50% {
        transform: scale(1.1);
        text-shadow: 0 0 20px rgba(40, 167, 69, 0.5);
    }
    100% {
        transform: scale(1);
        text-shadow: 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Pulse animation for info icon */
.pulse-info {
    animation: pulse-info 2s infinite;
}

@keyframes pulse-info {
    0% {
        transform: scale(1);
        text-shadow: 0 0 0 rgba(23, 162, 184, 0);
    }
    50% {
        transform: scale(1.1);
        text-shadow: 0 0 20px rgba(23, 162, 184, 0.5);
    }
    100% {
        transform: scale(1);
        text-shadow: 0 0 0 rgba(23, 162, 184, 0);
    }
}

/* Fade in animation for text */
.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Update the container styling */
#combined-alerts-container .status-message,
#combined-events-container .status-message {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    margin: 15px 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    overflow: hidden;
    height: auto !important;
}

/* Hide scrollbar when showing status message */
#combined-alerts-container:has(.status-message),
#combined-events-container:has(.status-message) {
    overflow: hidden !important;
    max-height: none !important;
}