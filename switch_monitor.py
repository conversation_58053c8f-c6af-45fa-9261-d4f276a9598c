from puresnmp import get, walk
import re
from concurrent.futures import Thread<PERSON>oolExecutor
import sqlite3
from datetime import datetime
import os
import time
import sys
from queue import Queue
from threading import Lock
import config

# Global queue for database operations
db_queue = Queue()
db_lock = Lock()

def init_database():
    conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS switch_status (
            timestamp TEXT,
            ip_address TEXT,
            model TEXT,
            image TEXT,
            total_stack_members INTEGER,
            online_stack_members INTEGER,
            status TEXT,
            PRIMARY KEY (timestamp, ip_address)
        )
    ''')
    
    # Add events table for switches
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS switch_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            switch_ip TEXT NOT NULL,
            event_type TEXT NOT NULL,
            event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            details TEXT
        )
    ''')
    
    conn.commit()
    return conn, cursor

def record_switch_event(switch_ip, event_type, details):
    try:
        conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO switch_events 
            (switch_ip, event_type, event_time, details)
            VALUES (?, ?, CURRENT_TIMESTAMP, ?)
        ''', (switch_ip, event_type, details))
        
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error recording switch event: {e}")

def get_switch_info(host, community):
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    try:
        # Get previous status and model to check for changes
        prev_status = None
        prev_model = "Unknown"
        try:
            conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, model 
                FROM switch_status 
                WHERE ip_address = ? AND model != 'Unknown'
                ORDER BY timestamp DESC 
                LIMIT 1
            ''', (host,))
            result = cursor.fetchone()
            if result:
                prev_status, prev_model = result
            conn.close()
        except Exception as e:
            print(f"Error getting previous status: {e}")

        # Get system description for primary switch
        value = get(host, community, '1.3.6.*******.1.0', timeout=2)
        if isinstance(value, bytes):
            value = value.decode('utf-8')
        
        # Extract image name and model
        image_match = re.search(r'\((.*?)\)', value)
        model_match = re.search(r'Software, (.*?) Software', value)

        if not (image_match and model_match):
            raise Exception("Failed to parse switch information")

        image_name = image_match.group(1)
        model = model_match.group(1)
        
        # Get stack members info
        stack_members = list(walk(host, community, '*******.*******.500.*******.7'))
        total_members = len(stack_members)
        
        if total_members > 0:
            stack_status = list(walk(host, community, '*******.*******.500.*******.3'))
            operational_members = len([s for s in stack_status if s[1] in [1, 2, 3]])
            status_str = f"{operational_members}/{total_members} online"
            if operational_members < total_members:
                status_str += " [WARNING: Some members offline]"
        else:
            status_str = "Standalone"
            total_members = 1
            operational_members = 1

        # Record event if status changed
        if prev_status == "Not responding":
            record_switch_event(host, "up", f"Switch is back online - {model}")
        elif prev_status is None:
            record_switch_event(host, "up", f"Switch discovered - {model}")

        # Queue database operation
        query = '''
            INSERT INTO switch_status 
            (timestamp, ip_address, model, image, total_stack_members, 
             online_stack_members, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        '''
        params = (timestamp, host, model, image_name, total_members, 
                 operational_members, status_str)
        db_queue.put((query, params))
        
        print(f"{host:<16} : Model: {model:<12} | Image: {image_name} | Stack: {status_str}")

    except Exception as e:
        error_msg = "Not responding"
        print(f"{host:<16} : {error_msg}")
        
        # Use previous model if available, otherwise use "Unknown"
        model_to_use = prev_model if prev_model != "Unknown" else "Unknown"
        
        # Record offline event if status changed
        if prev_status != "Not responding" and prev_status is not None:
            record_switch_event(host, "down", f"Switch is not responding - {model_to_use}")
        elif prev_status is None:
            record_switch_event(host, "down", f"Switch discovered offline - {model_to_use}")

        # Queue error status with previous model if available
        query = '''
            INSERT INTO switch_status 
            (timestamp, ip_address, model, image, total_stack_members, 
             online_stack_members, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        '''
        params = (timestamp, host, model_to_use, "Unknown", 0, 0, error_msg)
        db_queue.put((query, params))

def db_worker(db_queue):
    conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
    cursor = conn.cursor()
    
    while True:
        item = db_queue.get()
        if item is None:  # Poison pill to stop the worker
            break
            
        query, params = item
        try:
            with db_lock:
                cursor.execute(query, params)
                conn.commit()
        except Exception as e:
            print(f"Database error: {e}")
            
        db_queue.task_done()
    
    conn.close()

def get_latest_switch_data():
    try:
        conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
        cursor = conn.cursor()
        
        # Get the latest status for each switch
        cursor.execute('''
            WITH LatestTimestamps AS (
                SELECT ip_address, MAX(timestamp) as max_timestamp
                FROM switch_status
                GROUP BY ip_address
            )
            SELECT s.*
            FROM switch_status s
            JOIN LatestTimestamps lt 
                ON s.ip_address = lt.ip_address 
                AND s.timestamp = lt.max_timestamp
            ORDER BY s.ip_address
        ''')
        
        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    except Exception as e:
        print(f"Error getting switch data: {e}")
        return []

def get_switch_events():
    try:
        conn = sqlite3.connect(config.SWITCH_MONITORING_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                switch_ip,
                event_type,
                strftime('%H:%M:%S', event_time, 'localtime') as event_time,
                details
            FROM switch_events
            WHERE event_time >= datetime('now', '-1 hour')
            ORDER BY event_time DESC
        ''')
        
        columns = [col[0] for col in cursor.description]
        events = [dict(zip(columns, row)) for row in cursor.fetchall()]
        conn.close()
        return events
    except Exception as e:
        print(f"Error getting switch events: {e}")
        return []

def main():
    # Get configuration from config module
    community_string = config.SWITCH_COMMUNITY
    switch_ips = config.SWITCH_IPS

    # Initialize database
    init_database()
    
    # Start database worker thread
    from threading import Thread
    db_thread = Thread(target=db_worker, args=(db_queue,))
    db_thread.start()
    
    try:
        while True:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"\nQuerying all switches... {current_time}")
            print("-" * 85)
            
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(get_switch_info, ip, community_string) 
                          for ip in switch_ips]
                
                # Wait for all futures to complete
                for future in futures:
                    future.result()
            
            # Wait for database operations to complete
            db_queue.join()
            
            print("\nWaiting 30 seconds for next check...")
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user. Closing database connection...")
    finally:
        # Stop database worker
        db_queue.put(None)
        db_thread.join()
        print("Database connection closed.")

if __name__ == "__main__":
    main()