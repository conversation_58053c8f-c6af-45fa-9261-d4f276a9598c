<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Points Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="refresh-bar">
            <div class="d-flex justify-content-end align-items-center">
                <button onclick="updateAll()" class="btn btn-sm btn-outline-secondary me-2">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <small class="text-muted" id="last-update"></small>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">Recent Alerts</h5>
                    </div>
                    <div class="card-body">
                        <div id="combined-alerts-container"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Event History</h5>
                    </div>
                    <div class="card-body">
                        <div id="combined-events-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-12 col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Access Points Information</h5>
                    </div>
                    <div class="card-body">
                        <div id="ap-grid" class="ap-grid"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Network Switches</h5>
                    </div>
                    <div class="card-body">
                        <div id="switch-grid" class="switch-grid"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html> 