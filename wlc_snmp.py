try:
    from puresnmp import walk, get
except ImportError:
    from puresnmp.aio import walk, get

import sqlite3
from datetime import datetime
import os
import sys
import time
from queue import Queue
from threading import Thread, Lock
import config

db_queue = Queue()
db_lock = Lock()

def create_database():
    try:
        # Create directory if it doesn't exist (Windows)
        if sys.platform == 'win32':
            os.makedirs(os.path.dirname(config.ACCESS_POINTS_DB_PATH), exist_ok=True)

        conn = sqlite3.connect(config.ACCESS_POINTS_DB_PATH)
        cursor = conn.cursor()
        
        # Create table for access points
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS access_points (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ap_name TEXT NOT NULL,
                model TEXT NOT NULL,
                ip_address TEXT NOT NULL,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create table for events with explicit timestamp
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ap_name TEXT NOT NULL,
                event_type TEXT NOT NULL,
                event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                details TEXT
            )
        ''')

        # Create table for AP status
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ap_status (
                ap_name TEXT PRIMARY KEY,
                status TEXT NOT NULL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("Database initialized successfully")
    except Exception as e:
        print(f"Error creating database: {str(e)}")

def save_ap_info_to_db(ap_info):
    try:
        # Get current timestamp
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Use a local connection to get previous status
        conn = sqlite3.connect(config.ACCESS_POINTS_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT ap_name, last_seen FROM access_points')
        previous_status = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()
        
        # Insert or update AP information and record events
        for ap in ap_info:
            # Queue AP information update
            query = '''
                INSERT OR REPLACE INTO access_points (ap_name, model, ip_address, last_seen)
                VALUES (?, ?, ?, ?)
            '''
            params = (ap['name'], ap['model'], ap['ip'], current_time)
            db_queue.put((query, params))
            
            # Check status changes and record events
            if ap['name'] not in previous_status:
                record_event(ap['name'], 'up', f"AP discovered - {ap['model']}")
            else:
                last_seen = datetime.strptime(previous_status[ap['name']], '%Y-%m-%d %H:%M:%S')
                time_diff = (datetime.now() - last_seen).total_seconds() / 60
                
                if time_diff > 2:
                    record_event(ap['name'], 'up', f"AP is back online - {ap['model']}")
        
        # Check for APs that went offline
        current_ap_names = {ap['name'] for ap in ap_info}
        for prev_ap_name in previous_status:
            if prev_ap_name not in current_ap_names:
                record_event(prev_ap_name, 'down', "AP went offline")
        
        print("\nAP information queued for database update")
    except Exception as e:
        print(f"Error processing AP info: {str(e)}")

def record_event(ap_name, event_type, details):
    try:
        # Instead of direct database access, use the queue
        query = '''
            INSERT INTO events (ap_name, event_type, details, event_time)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        '''
        params = (ap_name, event_type, details)
        db_queue.put((query, params))
    except Exception as e:
        print(f"Error queueing event: {str(e)}")

def get_ap_info(host, community):
    try:
        print("\nGathering Access Point Information...")
        
        # OIDs
        ap_name_oid = '*******.*******.513.*******.5'
        ap_model_oid = '*******.4.1.14179.2.*******6'
        ap_ip_oid = '*******.4.1.14179.2.*******9'

        ap_names = []
        ap_models = []
        ap_ips = []
        ap_info = []
        
        # Get AP Names
        for oid, value in walk(host, community, ap_name_oid):
            if isinstance(value, bytes):
                value = value.decode()
            ap_names.append(value)

        # Get AP Models
        for oid, value in walk(host, community, ap_model_oid):
            if isinstance(value, bytes):
                value = value.decode()
            ap_models.append(value)

        # Get AP IP Addresses
        for oid, value in walk(host, community, ap_ip_oid):
            try:
                if isinstance(value, bytes):
                    try:
                        ip_address = value.decode()
                    except:
                        octets = [str(b) for b in value]
                        ip_address = '.'.join(octets)
                else:
                    octets = [(value >> 24) & 0xFF,
                             (value >> 16) & 0xFF,
                             (value >> 8) & 0xFF,
                             value & 0xFF]
                    ip_address = '.'.join(str(o) for o in octets)
                ap_ips.append(ip_address)
            except Exception as e:
                print(f"Error processing IP address: {e}")
                ap_ips.append("Unknown IP")

        # Combine names, models, and IPs
        for i in range(min(len(ap_names), len(ap_models), len(ap_ips))):
            ap_info.append({
                'name': ap_names[i],
                'model': ap_models[i],
                'ip': ap_ips[i]
            })

        # Sort APs by name
        ap_info.sort(key=lambda x: x['name'])
        
        print(f"\nFound {len(ap_info)} Access Points:")
        print("-" * 50)
        
        for ap in ap_info:
            print(f"AP Name: {ap['name']}")
            print(f"Model: {ap['model']}")
            print(f"IP Address: {ap['ip']}")
            print("-" * 50)

        # After printing AP info, save to database
        save_ap_info_to_db(ap_info)

        return ap_info
    except Exception as e:
        print(f"Error gathering AP information: {str(e)}")
        print(f"Error type: {type(e)}")
        print(f"Error details: {str(e)}")
        return []

def connect_wlc_snmpv3(host, community):
    try:
        print("Attempting to connect to WLC...")
        # Get system description
        description = get(host, community, '*******.*******.0')
        
        print("Successfully connected to WLC!")
        print(f"System Description: {description.decode() if isinstance(description, bytes) else description}")
        
        # Get AP Information
        get_ap_info(host, community)
        
        return True
            
    except Exception as e:
        print(f"Connection failed: {str(e)}")
        print("Error details:", str(e.__class__.__name__))
        return False

def view_database_contents():
    try:
        conn = sqlite3.connect(config.ACCESS_POINTS_DB_PATH)
        cursor = conn.cursor()
        
        print("\nDatabase Contents:")
        print("-" * 50)
        
        cursor.execute('SELECT * FROM access_points')
        results = cursor.fetchall()
        
        for row in results:
            print(f"ID: {row[0]}")
            print(f"AP Name: {row[1]}")
            print(f"Model: {row[2]}")
            print(f"IP Address: {row[3]}")
            print(f"Last Seen: {row[4]}")
            print("-" * 50)
            
        conn.close()
    except Exception as e:
        print(f"Error viewing database: {str(e)}")

def db_worker(db_queue):
    conn = sqlite3.connect(config.ACCESS_POINTS_DB_PATH, timeout=60)  # Increase timeout
    cursor = conn.cursor()
    
    while True:
        try:
            item = db_queue.get()
            if item is None:  # Poison pill to stop the worker
                break
                
            query, params = item
            with db_lock:
                cursor.execute(query, params)
                conn.commit()
                
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                print("Database locked, retrying in 1 second...")
                time.sleep(1)
                try:
                    with db_lock:
                        cursor.execute(query, params)
                        conn.commit()
                except Exception as retry_error:
                    print(f"Retry failed: {retry_error}")
        except Exception as e:
            print(f"Database error: {e}")
            
        finally:
            db_queue.task_done()
    
    conn.close()

def get_total_clients(host, community):
    try:
        # OID for client MAC addresses in Cisco WLC
        client_mac_oid = '*******.4.1.14179.*******.1'
        
        # Simple counter for total clients
        total_clients = 0
        
        # Walk through the client MAC OID and count
        for _, _ in walk(host, community, client_mac_oid):
            total_clients += 1
            
        return total_clients
        
    except Exception as e:
        print(f"Error getting client count: {str(e)}")
        return 0

if __name__ == "__main__":
    # Initialize database
    create_database()

    # Try to connect using configuration
    connect_wlc_snmpv3(config.WLC_HOST, config.WLC_COMMUNITY)
    view_database_contents()